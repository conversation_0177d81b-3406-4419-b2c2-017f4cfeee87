import React from "react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-100 text-gray-700 border-t">
      <div className="max-w-7xl mx-auto px-4 py-10">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          {/* Hỗ trợ khách hàng */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-gray-900">Hỗ trợ khách hàng</h4>
            <div className="space-y-2 text-xs">
              <p>
                Hotline: <a href="tel:1900-6035" className="text-blue-600">1900-6035</a>
                <span className="text-gray-500 ml-1">(1000 đ/phút, 8-21h kể cả T7, CN)</span>
              </p>
              <a href="https://hotro.tiki.vn/knowledge-base" className="block hover:text-blue-600" target="_blank" rel="noreferrer"><PERSON><PERSON><PERSON> câu hỏi thường gặp</a>
              <a href="https://hotro.tiki.vn/request/new-request" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Gửi yêu cầu hỗ trợ</a>
              <a href="https://hotro.tiki.vn/knowledge-base/post/898-lam-the-nao-de-toi-dat-hang-qua-website-tiki" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Hướng dẫn đặt hàng</a>
              <a href="https://hotro.tiki.vn/knowledge-base/post/838-cac-hinh-thuc-giao-hang-tai-tiki" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Phương thức vận chuyển</a>
              <a href="https://tiki.vn/chinh-sach-kiem-hang" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Chính sách kiểm hàng</a>
              <a href="https://hotro.tiki.vn/knowledge-base/post/802-chinh-sach-doi-tra-san-pham" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Chính sách đổi trả</a>
              <a href="https://tiki.vn/khuyen-mai/huong-dan-tra-gop" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Hướng dẫn trả góp</a>
              <a href="https://hotro.tiki.vn/knowledge-base/post/883-dich-vu-giao-hang-tu-nuoc-ngoai" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Chính sách hàng nhập khẩu</a>
              <div className="pt-2 space-y-1">
                <p>Hỗ trợ khách hàng: <a href="mailto:<EMAIL>" className="text-blue-600"><EMAIL></a></p>
                <p>Báo lỗi bảo mật: <a href="mailto:<EMAIL>" className="text-blue-600"><EMAIL></a></p>
              </div>
            </div>
          </div>

          {/* Về Tiki */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-gray-900">Về Tiki</h4>
            <div className="space-y-2 text-xs">
              <a href="https://tiki.vn/thong-tin/gioi-thieu-ve-tiki" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Giới thiệu Tiki</a>
              <a href="https://tiki.vn/blog/" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Tiki Blog</a>
              <a href="https://tuyendung.tiki.vn/" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Tuyển dụng</a>
              <a href="https://tiki.vn/bao-mat-thanh-toan" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Chính sách bảo mật thanh toán</a>
              <a href="https://tiki.vn/bao-mat-thong-tin-ca-nhan" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Chính sách bảo mật thông tin cá nhân</a>
              <a href="https://hotro.tiki.vn/knowledge-base/post/778-chinh-sach-giai-quyet-khieu-nai" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Chính sách giải quyết khiếu nại</a>
              <a href="https://hotro.tiki.vn/knowledge-base/post/850-dieu-khoan-su-dung" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Điều khoản sử dụng</a>
              <a href="https://hotro.tiki.vn/knowledge-base/post/979-tiki-xu-la-gi?-gia-tri-quy-doi-nhu-the-nao?" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Giới thiệu Tiki Xu</a>
              <a href="https://tiki.vn/khuyen-mai/tiki-tiep-thi-lien-ket" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Tiếp thị liên kết cùng Tiki</a>
              <a href="https://tiki.vn/khuyen-mai/ban-hang-doanh-nghiep" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Bán hàng doanh nghiệp</a>
              <a href="https://www.tikinow.biz/%C4%91i%E1%BB%81u-kho%E1%BA%A3n-v%E1%BA%ADn-chuy%E1%BB%83n" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Điều kiện vận chuyển</a>
            </div>
          </div>

          {/* Hợp tác và liên kết */}
          <div className="space-y-3">
            <h4 className="text-sm font-semibold text-gray-900">Hợp tác và liên kết</h4>
            <div className="space-y-2 text-xs">
              <a href="https://tiki.vn/quy-che-hoat-dong-sgdtmdt" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Quy chế hoạt động Sàn GDTMĐT</a>
              <a href="https://tiki.vn/khuyen-mai/ban-hang-cung-tiki" className="block hover:text-blue-600" target="_blank" rel="noreferrer">Bán hàng cùng Tiki</a>
            </div>
            
            <div className="mt-6">
              <h4 className="text-sm font-semibold mb-3 text-gray-900">Chứng nhận bởi</h4>
              <div className="flex gap-2">
                <a href="https://hotro.tiki.vn/knowledge-base" target="_blank" rel="noreferrer" className="h-8">
                  <img src="https://frontend.tikicdn.com/_desktop-next/static/img/footer/bo-cong-thuong-2.png" className="w-8 h-8" alt="bo-cong-thuong-2" />
                </a>
                <a href="http://online.gov.vn/Home/WebDetails/21193" target="_blank" rel="noreferrer" className="h-8">
                  <img src="https://frontend.tikicdn.com/_desktop-next/static/img/footer/bo-cong-thuong.svg" className="h-8 w-auto" alt="bo-cong-thuong" />
                </a>
                <a href="https://www.dmca.com/Protection/Status.aspx?ID=388d758c-6722-4245-a2b0-1d2415e70127&refurl=https://tiki.vn/" title="DMCA.com Protection Status" target="_blank" rel="noreferrer" className="h-8">
                  <img src="https://images.dmca.com/Badges/dmca_protected_sml_120y.png?ID=388d758c-6722-4245-a2b0-1d2415e70127" alt="DMCA.com Protection Status" className="w-8 h-8" />
                </a>
              </div>
            </div>
          </div>

          {/* Phương thức thanh toán */}
          <div className="block payment-information">
            <h4 className="sc-4ce82f3c-4 aMfcf text-sm font-semibold mb-4 text-gray-900">Phương thức thanh toán</h4>
            <p className="payment flex flex-wrap gap-1 mb-6">
              <span className="icon">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <defs>
                    <radialGradient id="paint0_radial_3173:26341" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(16 16) rotate(90) scale(17.7561 28.4098)">
                      <stop stopColor="#004D9B"></stop>
                      <stop offset="1" stopColor="#002462"></stop>
                    </radialGradient>
                    <linearGradient id="paint1_linear_3173:26341" x1="-13.6766" y1="0.916464" x2="3.09852" y2="38.1376" gradientUnits="userSpaceOnUse">
                      <stop stopColor="#33C3F5"></stop>
                      <stop offset="1" stopColor="#2241EC"></stop>
                      <stop offset="1" stopColor="#1D3CE7"></stop>
                    </linearGradient>
                  </defs>
                  <path d="M0 12.0235C0 9.9151 0 8.86088 0.410328 8.05557C0.771263 7.34719 1.34719 6.77126 2.05557 6.41033C2.86088 6 3.9151 6 6.02353 6H25.9765C28.0849 6 29.1391 6 29.9444 6.41033C30.6528 6.77126 31.2287 7.34719 31.5897 8.05557C32 8.86088 32 9.9151 32 12.0235V19.9765C32 22.0849 32 23.1391 31.5897 23.9444C31.2287 24.6528 30.6528 25.2287 29.9444 25.5897C29.1391 26 28.0849 26 25.9765 26H6.02353C3.9151 26 2.86088 26 2.05557 25.5897C1.34719 25.2287 0.771263 24.6528 0.410328 23.9444C0 23.1391 0 22.0849 0 19.9765V12.0235Z" fill="url(#paint0_radial_3173:26341)"></path>
                  <mask id="mask0_3173:26341" maskUnits="userSpaceOnUse" x="0" y="6" width="32" height="20" style={{maskType: 'alpha'}}>
                    <rect y="6" width="32" height="20" fill="white"></rect>
                  </mask>
                  <g mask="url(#mask0_3173:26341)">
                    <path fillRule="evenodd" clipRule="evenodd" d="M13.9036 10.25C12.9282 10.25 12.137 11.0445 12.137 12.0248C12.137 13.0043 12.9282 13.7997 13.9036 13.7997C14.879 13.7997 15.6711 13.0043 15.6711 12.0248C15.6711 11.0445 14.879 10.25 13.9036 10.25ZM13.9036 13.492C13.1553 13.492 12.5512 12.8835 12.5512 12.1341C12.5512 12.0047 12.5693 11.8811 12.6037 11.7613H15.2044C15.2388 11.8811 15.2579 12.0047 15.2579 12.1341C15.2579 12.8835 14.6518 13.492 13.9036 13.492ZM22.7325 10.25C21.7571 10.25 20.9649 11.0445 20.9649 12.0248C20.9649 13.0043 21.7571 13.7997 22.7325 13.7997C23.7088 13.7997 24.5 13.0043 24.5 12.0248C24.5 11.0445 23.7088 10.25 22.7325 10.25ZM22.7325 13.492C21.9852 13.492 21.3791 12.8835 21.3791 12.1341C21.3791 12.0047 21.3982 11.8811 21.4316 11.7613H24.0324C24.0667 11.8811 24.0858 12.0047 24.0858 12.1341C24.0858 12.8835 23.4807 13.492 22.7325 13.492ZM15.5169 15.2111C15.5169 14.7987 15.8213 14.4667 16.2785 14.4667C16.7146 14.4667 17.0391 14.7485 17.0391 15.2111V17.3838L19.5368 14.7485C19.6475 14.6275 19.8403 14.4667 20.1247 14.4667C20.5103 14.4667 20.8758 14.7589 20.8758 15.1912C20.8758 15.4523 20.7135 15.6642 20.3786 15.9962L18.4602 17.8671L20.8052 20.2905C21.0485 20.5326 21.2308 20.754 21.2308 21.0548C21.2308 21.5287 20.8558 21.75 20.4396 21.75C20.1457 21.75 19.9529 21.5788 19.6685 21.2771L17.0391 18.4602V21.0255C17.0391 21.418 16.7347 21.75 16.2785 21.75C15.8414 21.75 15.5169 21.4681 15.5169 21.0255V15.2111ZM9.29616 15.8669H8.19002C7.70233 15.8669 7.5 15.512 7.5 15.1668C7.5 14.8119 7.75387 14.4667 8.19002 14.4667H11.9255C12.3617 14.4667 12.6155 14.8119 12.6155 15.1668C12.6155 15.512 12.4123 15.8669 11.9255 15.8669H10.8194V20.9593C10.8194 21.4667 10.4949 21.75 10.0578 21.75C9.62066 21.75 9.29616 21.4667 9.29616 20.9593V15.8669ZM22.0067 15.2518C22.0067 14.7485 22.3322 14.4667 22.7684 14.4667C23.2045 14.4667 23.529 14.7485 23.529 15.2518V20.9659C23.529 21.4681 23.2045 21.75 22.7684 21.75C22.3322 21.75 22.0067 21.4681 22.0067 20.9659V15.2518ZM13.2263 15.2518C13.2263 14.7485 13.5508 14.4667 13.987 14.4667C14.4241 14.4667 14.7486 14.7485 14.7486 15.2518V20.9659C14.7486 21.4681 14.4241 21.75 13.987 21.75C13.5508 21.75 13.2263 21.4681 13.2263 20.9659V15.2518Z" fill="white"></path>
                    <path fillRule="evenodd" clipRule="evenodd" d="M2.25 16C2.25 8.40608 8.40608 2.25 16 2.25C23.5939 2.25 29.75 8.40608 29.75 16C29.75 17.1313 29.6131 18.2459 29.3447 19.3257C27.8336 25.4072 22.3523 29.75 16 29.75C8.40608 29.75 2.25 23.5939 2.25 16ZM26.9052 16C26.9052 9.97724 22.0228 5.09483 16 5.09483C9.97724 5.09483 5.09483 9.97724 5.09483 16C5.09483 22.0228 9.97724 26.9052 16 26.9052C21.0378 26.9052 25.3861 23.46 26.5839 18.6397C26.7965 17.7838 26.9052 16.8996 26.9052 16Z" fill="url(#paint1_linear_3173:26341)"></path>
                  </g>
                </svg>
              </span>
              <span className="icon">
                <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <mask id="mask0_1329:61134" maskUnits="userSpaceOnUse" x="0" y="6" width="32" height="20" style={{maskType: 'alpha'}}>
                    <rect y="6" width="32" height="20" rx="1.81818" fill="white"></rect>
                  </mask>
                  <g mask="url(#mask0_1329:61134)">
                    <rect opacity="0.01" x="-1" y="4" width="34.04" height="23" fill="white"></rect>
                    <path fillRule="evenodd" clipRule="evenodd" d="M9.8116 10.6916L7.26655 17.2114L6.23703 11.6676C6.11637 11.0489 5.63944 10.6916 5.10955 10.6916H0.949103L0.891235 10.9696C1.74526 11.1578 2.7154 11.4606 3.30354 11.7849C3.66323 11.9827 3.76572 12.156 3.88411 12.6263L5.83384 20.2704H8.41786L12.3794 10.6916H9.8116ZM13.4285 10.6916L11.4062 20.2704H13.8514L15.8726 10.6916H13.4285ZM27.4466 13.2791L28.1868 16.8763H26.1588L27.4466 13.2791ZM27.0873 10.6916C26.6187 10.6916 26.2234 10.9688 26.0472 11.3943L22.3792 20.2704H24.945L25.4556 18.8405H28.5911L28.8876 20.2704H31.149L29.1754 10.6916H27.0873ZM16.5398 13.6828C16.5224 15.062 17.7528 15.8321 18.6794 16.2895C19.6318 16.7594 19.9514 17.0603 19.948 17.4805C19.9408 18.1229 19.1881 18.4066 18.4842 18.4177C17.2558 18.4373 16.5417 18.0815 15.9736 17.8128L15.5311 19.9112C16.1007 20.1776 17.1555 20.4095 18.2497 20.4199C20.8175 20.4199 22.4972 19.1353 22.5063 17.1431C22.5165 14.6154 19.0565 14.4755 19.0799 13.3455C19.0883 13.0031 19.4109 12.6374 20.1178 12.5447C20.4676 12.4975 21.4332 12.4615 22.5282 12.9725L22.9579 10.9423C22.3693 10.725 21.6125 10.5168 20.67 10.5168C18.2531 10.5168 16.5534 11.8186 16.5398 13.6828Z" fill="#1A1F71"></path>
                  </g>
                </svg>
              </span>
              <span className="icon">
                <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect opacity="0.01" x="1" y="6.6665" width="30" height="20" fill="white"></rect>
                  <rect x="12.3877" y="10.1254" width="7.17949" height="12.9247" fill="#FF5F00"></rect>
                  <path d="M12.8434 16.5889C12.8407 14.0664 13.9964 11.6828 15.9773 10.1254C12.6131 7.47702 7.78209 7.86278 4.87927 11.0116C1.97644 14.1604 1.97644 19.0151 4.87927 22.1639C7.78209 25.3127 12.6131 25.6985 15.9773 23.0501C13.997 21.4931 12.8414 19.1106 12.8434 16.5889Z" fill="#EB001B"></path>
                  <path d="M29.2539 16.5889C29.2538 19.7358 27.46 22.6064 24.6343 23.9815C21.8087 25.3567 18.4472 24.995 15.9775 23.0501C17.9569 21.4918 19.1126 19.1096 19.1126 16.5877C19.1126 14.0659 17.9569 11.6837 15.9775 10.1254C18.4472 8.18045 21.8087 7.81875 24.6343 9.19392C27.46 10.5691 29.2538 13.4397 29.2539 16.5866V16.5889Z" fill="#F79E1B"></path>
                </svg>
              </span>
              <span className="icon">
                <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <rect opacity="0.01" y="5.6665" width="32" height="21.3333" fill="white"></rect>
                  <path d="M29.5619 22.6087C29.5619 24.9501 27.6552 26.8567 25.3138 26.8567H2.2002V10.0317C2.2002 7.6902 4.10682 5.78358 6.44828 5.78358H29.5619V22.6087Z" fill="white"></path>
                  <path d="M22.0356 18.2937H23.7917C23.8419 18.2937 23.959 18.277 24.0092 18.277C24.3437 18.2101 24.628 17.909 24.628 17.4909C24.628 17.0895 24.3437 16.7885 24.0092 16.7048C23.959 16.6881 23.8586 16.6881 23.7917 16.6881H22.0356V18.2937Z" fill="url(#paint0_linear)"></path>
                  <path d="M23.5909 7.20518C21.9184 7.20518 20.547 8.55988 20.547 10.2491V13.4101H24.8453C24.9456 13.4101 25.0627 13.4101 25.1463 13.4268C26.1163 13.477 26.8355 13.9787 26.8355 14.8484C26.8355 15.5341 26.3505 16.1195 25.4473 16.2365V16.27C26.4341 16.3369 27.1867 16.8888 27.1867 17.7418C27.1867 18.6616 26.3505 19.2637 25.2467 19.2637H20.5303V25.4519H24.9958C26.6683 25.4519 28.0397 24.0972 28.0397 22.408V7.20518H23.5909Z" fill="url(#paint1_linear)"></path>
                </svg>
              </span>
              {/* More payment icons would go here */}
            </p>
            
            <h4 style={{margin:'24px 0 12px'}} className="sc-4ce82f3c-4 aMfcf text-sm font-semibold text-gray-900">Dịch vụ giao hàng</h4>
            <p>
              <a rel="nofollow noreferrer" href="https://tikinow.vn?src=footer" target="_blank">
                <img src="https://salt.tikicdn.com/ts/upload/74/56/ab/e71563afb23e3f34a148fe1b7d3413c5.png" width="109" height="33" style={{marginLeft:'-9px',marginTop:'-8px'}} alt="tikinow-icon" />
              </a>
            </p>
          </div>

          {/* Kết nối với chúng tôi */}
          <div className="block">
            <h4 className="sc-4ce82f3c-4 aMfcf text-sm font-semibold mb-4 text-gray-900">Kết nối với chúng tôi</h4>
            <p className="flex gap-2 mb-6">
              <a rel="nofollow noreferrer" href="https://www.facebook.com/tiki.vn/" className="icon" target="_blank" title="Facebook">
                <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M0 16.6665C0 7.82995 7.16344 0.666504 16 0.666504C24.8366 0.666504 32 7.82995 32 16.6665C32 25.5031 24.8366 32.6665 16 32.6665C7.16344 32.6665 0 25.5031 0 16.6665Z" fill="#3B5998"></path>
                  <path d="M17.6676 26.0742V17.3693H20.0706L20.389 14.3696H17.6676L17.6717 12.8682C17.6717 12.0858 17.7461 11.6666 18.8698 11.6666H20.372V8.6665H17.9687C15.082 8.6665 14.066 10.1217 14.066 12.5689V14.3699H12.2666V17.3696H14.066V26.0742H17.6676Z" fill="white"></path>
                </svg>
              </a>
              <a rel="nofollow noreferrer" href="https://www.youtube.com/user/TikiVBlog" className="icon" target="_blank" title="Youtube">
                <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M0 16.6665C0 7.82995 7.16344 0.666504 16 0.666504C24.8366 0.666504 32 7.82995 32 16.6665C32 25.5031 24.8366 32.6665 16 32.6665C7.16344 32.6665 0 25.5031 0 16.6665Z" fill="#FF0000"></path>
                  <path d="M24.1768 12.7153C23.9805 11.9613 23.4022 11.3675 22.6679 11.166C21.3371 10.7998 16.0001 10.7998 16.0001 10.7998C16.0001 10.7998 10.6632 10.7998 9.3323 11.166C8.59795 11.3675 8.01962 11.9613 7.82335 12.7153C7.4668 14.0818 7.4668 16.9331 7.4668 16.9331C7.4668 16.9331 7.4668 19.7843 7.82335 21.151C8.01962 21.905 8.59795 22.4987 9.3323 22.7003C10.6632 23.0665 16.0001 23.0665 16.0001 23.0665C16.0001 23.0665 21.3371 23.0665 22.6679 22.7003C23.4022 22.4987 23.9805 21.905 24.1768 21.151C24.5335 19.7843 24.5335 16.9331 24.5335 16.9331C24.5335 16.9331 24.5335 14.0818 24.1768 12.7153Z" fill="white"></path>
                  <path d="M14.3999 19.8665V14.5332L18.6666 17.2L14.3999 19.8665Z" fill="#FF0000"></path>
                </svg>
              </a>
              <a rel="nofollow noreferrer" href="http://zalo.me/589673439383195103" className="icon" target="_blank" title="Zalo">
                <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M0 16.6665C0 7.82995 7.16344 0.666504 16 0.666504C24.8366 0.666504 32 7.82995 32 16.6665C32 25.5031 24.8366 32.6665 16 32.6665C7.16344 32.6665 0 25.5031 0 16.6665Z" fill="#3171F6"></path>
                  <path fillRule="evenodd" clipRule="evenodd" d="M16.0002 5.99984C10.1091 5.99984 5.3335 10.4556 5.3335 15.9522C5.3335 19.0351 6.83597 21.7903 9.19473 23.6158V27.3332L12.8261 25.4565C13.8287 25.7477 14.8948 25.9046 16.0002 25.9046C21.8912 25.9046 26.6668 21.4488 26.6668 15.9522C26.6668 10.4556 21.8912 5.99984 16.0002 5.99984ZM9.87701 18.0804C10.6612 18.0804 11.3932 18.0759 12.125 18.0821C12.5362 18.0856 12.7584 18.2607 12.7962 18.5845C12.8442 18.9944 12.605 19.2664 12.1609 19.2714C11.3233 19.2809 10.4855 19.275 9.64768 19.275C9.40587 19.275 9.16349 19.2835 8.92244 19.2696C8.62187 19.2523 8.32787 19.1928 8.18415 18.8827C8.04006 18.5719 8.14015 18.293 8.33911 18.04C9.13968 17.0219 9.9412 16.0047 10.7422 14.9869C10.7898 14.9265 10.8357 14.8648 10.882 14.8043C10.833 14.7159 10.7554 14.7555 10.6949 14.7551C10.1336 14.7516 9.57215 14.7556 9.01082 14.7511C8.88254 14.7501 8.75044 14.7398 8.62701 14.7074C8.36663 14.6391 8.20854 14.4307 8.20644 14.182C8.20434 13.9329 8.35768 13.722 8.61749 13.6487C8.74025 13.6141 8.87282 13.6021 9.00111 13.6016C9.9252 13.5978 10.8493 13.5981 11.7734 13.6011C11.9367 13.6016 12.1011 13.6058 12.2597 13.6606C12.6101 13.7815 12.7643 14.1045 12.6219 14.4465C12.4978 14.7442 12.3001 14.9973 12.1027 15.2486C11.4252 16.1108 10.7452 16.9709 10.0663 17.8322C10.0136 17.899 9.96292 17.9676 9.87701 18.0804ZM14.0567 17.2472C14.0617 17.4255 14.1205 17.6652 14.2747 17.8732C14.6102 18.3257 15.2984 18.3243 15.6337 17.8723C15.9242 17.4805 15.9227 16.8304 15.6319 16.4389C15.4782 16.2321 15.273 16.1238 15.0169 16.1087C14.4487 16.0753 14.0509 16.5148 14.0567 17.2472ZM15.8889 15.3525C16.0151 15.1936 16.1404 15.0439 16.3538 15.0005C16.7609 14.9174 17.147 15.182 17.1525 15.596C17.1661 16.6319 17.161 17.668 17.1549 18.7041C17.1532 18.987 16.9789 19.2039 16.7239 19.2906C16.4567 19.3814 16.1783 19.3152 15.9998 19.09C15.9124 18.9797 15.875 18.9607 15.7531 19.0596C15.2812 19.4422 14.7489 19.5091 14.1735 19.3225C13.2505 19.023 12.8705 18.3038 12.7703 17.4228C12.6626 16.4766 12.9776 15.6645 13.8246 15.1666C14.5277 14.7532 15.2421 14.788 15.8889 15.3525ZM20.7838 17.1508C20.7824 17.416 20.8448 17.6634 21.0047 17.8783C21.3324 18.3189 22.0136 18.3224 22.348 17.8879C22.6494 17.4962 22.6504 16.8305 22.353 16.4346C22.1979 16.2282 21.9918 16.1217 21.7364 16.1082C21.1766 16.0785 20.7862 16.5065 20.7838 17.1508ZM19.4806 17.276C19.4411 15.9452 20.3142 14.9509 21.556 14.9127C22.8756 14.8721 23.8436 15.7594 23.883 17.0529C23.9229 18.3626 23.1194 19.2917 21.8803 19.416C20.5341 19.5509 19.4614 18.57 19.4806 17.276ZM19.0266 16.2455C19.0266 17.0484 19.0306 17.8513 19.025 18.6542C19.0218 19.1134 18.6166 19.4239 18.1809 19.3139C17.9192 19.2479 17.7236 18.9703 17.7231 18.6468C17.7211 17.2741 17.7223 15.9014 17.7223 14.5287C17.7223 14.287 17.7189 14.0451 17.7231 13.8035C17.7301 13.4051 17.9837 13.1465 18.3649 13.1428C18.7586 13.1389 19.0226 13.3985 19.0252 13.811C19.0302 14.6225 19.0266 15.434 19.0266 16.2455Z" fill="white"></path>
                </svg>
              </a>
            </p>
            
            <h4 className="sc-4ce82f3c-4 aMfcf store-title text-sm font-semibold mb-3 text-gray-900">Tải ứng dụng trên điện thoại</h4>
            <div className="flex gap-3">
              <img src="https://frontend.tikicdn.com/_desktop-next/static/img/footer/qrcode.png" width="80" height="80" alt="tiki-qr" />
              <div style={{display:'flex',flexDirection:'column',justifyContent:'space-between',alignItems:'center',height:'80px'}}>
                <a rel="nofollow noreferrer" href="https://itunes.apple.com/vn/app/id958100553" target="_blank" aria-label="" style={{height:'36px'}}>
                  <img src="https://frontend.tikicdn.com/_desktop-next/static/img/icons/appstore.png" width="122" alt="tiki-app-store" />
                </a>
                <a rel="nofollow noreferrer" href="https://play.google.com/store/apps/details?id=vn.tiki.app.tikiandroid" target="_blank" aria-label="" style={{height:'36px'}}>
                  <img src="https://frontend.tikicdn.com/_desktop-next/static/img/icons/playstore.png" width="122" alt="tiki-google-play" />
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
