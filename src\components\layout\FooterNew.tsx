import React from "react";
import {
  Phone,
  QrCode,
  Facebook,
  Youtube,
  MessageCircle,
} from "lucide-react";

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-100 text-gray-700 py-10 px-4 md:px-8 border-t">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-8 mb-8">
          {/* Hỗ trợ khách hàng */}
          <div>
            <h3 className="text-sm font-semibold mb-4 text-gray-900">Hỗ trợ khách hàng</h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <Phone size={14} className="text-gray-500" />
                <span>Hotline: 1900-6035</span>
              </li>
              <li className="text-xs text-gray-500">(1000 đ/phút, 8-21h cả T7, CN)</li>
              <li><a href="#" className="hover:text-blue-600"><PERSON><PERSON><PERSON> câu hỏi thường gặp</a></li>
              <li><a href="#" className="hover:text-blue-600">Gửi yêu cầu hỗ trợ</a></li>
              <li><a href="#" className="hover:text-blue-600">Hướng dẫn đặt hàng</a></li>
              <li><a href="#" className="hover:text-blue-600">Phương thức vận chuyển</a></li>
              <li><a href="#" className="hover:text-blue-600">Chính sách kiểm hàng</a></li>
              <li><a href="#" className="hover:text-blue-600">Chính sách đổi trả</a></li>
              <li><a href="#" className="hover:text-blue-600">Hướng dẫn trả góp</a></li>
              <li><a href="#" className="hover:text-blue-600">Chính sách hàng nhập khẩu</a></li>
              <li className="mt-4">
                <p className="text-xs">Hỗ trợ khách hàng:</p>
                <p className="text-xs"><EMAIL></p>
              </li>
              <li>
                <p className="text-xs">Báo lỗi bảo mật:</p>
                <p className="text-xs"><EMAIL></p>
              </li>
            </ul>
          </div>

          {/* Về Tiki */}
          <div>
            <h3 className="text-sm font-semibold mb-4 text-gray-900">Về Tiki</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-blue-600">Giới thiệu Tiki</a></li>
              <li><a href="#" className="hover:text-blue-600">Tiki Blog</a></li>
              <li><a href="#" className="hover:text-blue-600">Tuyển dụng</a></li>
              <li><a href="#" className="hover:text-blue-600">Chính sách bảo mật thanh toán</a></li>
              <li><a href="#" className="hover:text-blue-600">Chính sách bảo mật thông tin cá nhân</a></li>
              <li><a href="#" className="hover:text-blue-600">Chính sách giải quyết khiếu nại</a></li>
              <li><a href="#" className="hover:text-blue-600">Điều khoản sử dụng</a></li>
              <li><a href="#" className="hover:text-blue-600">Giới thiệu Tiki Xu</a></li>
              <li><a href="#" className="hover:text-blue-600">Tiếp thị liên kết cùng Tiki</a></li>
              <li><a href="#" className="hover:text-blue-600">Bán hàng doanh nghiệp</a></li>
              <li><a href="#" className="hover:text-blue-600">Điều kiện vận chuyển</a></li>
            </ul>
          </div>

          {/* Hợp tác và liên kết */}
          <div>
            <h3 className="text-sm font-semibold mb-4 text-gray-900">Hợp tác và liên kết</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="hover:text-blue-600">Quy chế hoạt động Sàn GDTMĐT</a></li>
              <li><a href="#" className="hover:text-blue-600">Bán hàng cùng Tiki</a></li>
            </ul>

            <div className="mt-6">
              <h4 className="text-sm font-semibold mb-4 text-gray-900">Chứng nhận bởi</h4>
              <div className="flex items-center gap-2">
                <div className="w-16 h-8 bg-red-600 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">BCT</span>
                </div>
                <div className="w-16 h-8 bg-green-600 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">✓</span>
                </div>
                <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                  <span className="text-white text-xs font-bold">D</span>
                </div>
              </div>
            </div>
          </div>

          {/* Phương thức thanh toán */}
          <div>
            <h3 className="text-sm font-semibold mb-4 text-gray-900">Phương thức thanh toán</h3>
            <div className="grid grid-cols-3 gap-2 mb-6">
              <div className="bg-white border rounded p-2 flex items-center justify-center h-8">
                <span className="text-blue-600 font-bold text-xs">TiKi</span>
              </div>
              <div className="bg-white border rounded p-2 flex items-center justify-center h-8">
                <span className="text-blue-600 font-bold text-xs">VISA</span>
              </div>
              <div className="bg-white border rounded p-2 flex items-center justify-center h-8">
                <div className="w-4 h-4 bg-gradient-to-r from-red-500 to-orange-500 rounded-full"></div>
              </div>
              <div className="bg-white border rounded p-2 flex items-center justify-center h-8">
                <span className="text-blue-600 font-bold text-xs">JCB</span>
              </div>
              <div className="bg-white border rounded p-2 flex items-center justify-center h-8">
                <span className="text-gray-600 font-bold text-xs">ATM</span>
              </div>
              <div className="bg-white border rounded p-2 flex items-center justify-center h-8">
                <span className="text-pink-600 font-bold text-xs">momo</span>
              </div>
              <div className="bg-white border rounded p-2 flex items-center justify-center h-8">
                <span className="text-blue-500 font-bold text-xs">Zalo</span>
              </div>
              <div className="bg-white border rounded p-2 flex items-center justify-center h-8">
                <div className="w-4 h-4 bg-red-600 rounded-full"></div>
              </div>
              <div className="bg-white border rounded p-2 flex items-center justify-center h-8">
                <span className="text-blue-600 font-bold text-xs">VN</span>
              </div>
            </div>
            <div className="bg-white border rounded p-2 text-center mb-4">
              <span className="text-gray-600 font-bold text-xs">TRẢ GÓP 0%</span>
            </div>

            <div>
              <h4 className="text-sm font-semibold mb-3 text-gray-900">Dịch vụ giao hàng</h4>
              <div className="bg-blue-600 rounded p-3">
                <span className="text-white font-bold text-lg">TIKINOW</span>
              </div>
            </div>
          </div>

          {/* Kết nối với chúng tôi */}
          <div>
            <h3 className="text-sm font-semibold mb-4 text-gray-900">Kết nối với chúng tôi</h3>
            <div className="flex gap-2 mb-6">
              <a href="#" className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700">
                <Facebook size={20} className="text-white" />
              </a>
              <a href="#" className="w-10 h-10 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700">
                <Youtube size={20} className="text-white" />
              </a>
              <a href="#" className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-600">
                <MessageCircle size={20} className="text-white" />
              </a>
            </div>

            <div>
              <h4 className="text-sm font-semibold mb-3 text-gray-900">Tải ứng dụng trên điện thoại</h4>
              <div className="flex gap-3">
                <div className="flex-1">
                  <QrCode size={80} className="text-gray-400 mb-2" />
                </div>
                <div className="flex-1 space-y-2">
                  <div className="bg-black text-white rounded p-2 text-xs flex items-center gap-2">
                    <div className="w-6 h-6 bg-white rounded flex items-center justify-center">
                      <span className="text-black text-xs">🍎</span>
                    </div>
                    <div>
                      <div className="text-xs">Tải về trên</div>
                      <div className="font-semibold">App Store</div>
                    </div>
                  </div>
                  <div className="bg-black text-white rounded p-2 text-xs flex items-center gap-2">
                    <div className="w-6 h-6 bg-white rounded flex items-center justify-center">
                      <span className="text-black text-xs">▶</span>
                    </div>
                    <div>
                      <div className="text-xs">TẢI VỀ TRÊN</div>
                      <div className="font-semibold">Google Play</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t pt-6">
          <div className="text-center text-xs text-gray-500">
            <p>© 2023 - Bản quyền của Công Ty Cổ Phần Tiki</p>
            <p className="mt-1">
              Địa chỉ trụ sở: Tòa nhà Viettel, Số 285, Đường Cách Mạng Tháng 8, Phường 12, Quận 10, Thành phố Hồ Chí Minh
            </p>
            <p className="mt-1">
              Tiki nhận đặt hàng trực tuyến và giao hàng tận nơi, không hỗ trợ đặt mua và nhận hàng trực tiếp tại văn phòng hoặc trung tâm xử lý đơn hàng
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
